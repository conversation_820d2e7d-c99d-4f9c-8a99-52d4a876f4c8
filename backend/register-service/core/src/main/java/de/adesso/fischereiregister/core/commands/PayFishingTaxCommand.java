package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public record PayFishingTaxCommand(
        @TargetAggregateIdentifier UUID registerId,
        TaxConsentInfo consentInfo,
        Person person, // Deprecated
        String salt,
        List<Tax> taxes,
        UserDetails userDetails,
        Instant mockedTimestamp
) {

    public PayFishingTaxCommand {
        assert taxes != null : "The list of taxes should not be null";
    }
}

