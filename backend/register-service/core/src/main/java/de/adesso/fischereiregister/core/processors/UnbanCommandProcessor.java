package de.adesso.fischereiregister.core.processors;

import de.adesso.fischereiregister.core.commands.UnbanCommand;
import de.adesso.fischereiregister.core.events.AxonEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UnbanCommandProcessor implements CommandProcessor<UnbanCommand> {
    @Override
    public List<AxonEvent> process(UnbanCommand command, RegisterEntry registerEntry) {
        return List.of(new UnbannedEvent(command.registerEntryId(), registerEntry.getJurisdiction(), command.mockedTimestamp()));
    }
}
