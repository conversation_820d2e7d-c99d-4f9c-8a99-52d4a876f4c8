package de.adesso.fischereiregister.core.events;

import de.adesso.fischereiregister.core.model.Jurisdiction;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.Instant;
import java.util.UUID;

public record UnbannedEvent(
        @TargetAggregateIdentifier UUID registerEntryId,
        Jurisdiction jurisdiction,
        Instant mockedTimestamp
) implements AxonEvent {
}
