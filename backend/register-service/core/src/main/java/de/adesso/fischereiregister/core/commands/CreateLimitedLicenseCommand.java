package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.core.model.consent.LimitedLicenseConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Command to create a limited license.
 */
public record CreateLimitedLicenseCommand(
        @TargetAggregateIdentifier UUID registerId,
        String salt,
        LimitedLicenseConsentInfo consentInfo,
        Person person,
        List<Fee> fees,
        List<Tax> taxes,
        ValidityPeriod validityPeriod,
        LimitedLicenseApproval limitedLicenseApproval,
        UserDetails userDetails,
        Instant mockedTimestamp) {

    public CreateLimitedLicenseCommand {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
    }

}
