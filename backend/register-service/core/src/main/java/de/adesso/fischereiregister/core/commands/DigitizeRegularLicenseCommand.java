package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Fee;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.ConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * Starts on the start screen.
 * (No fishing certificat available and no Register available in the system.)
 */

public record DigitizeRegularLicenseCommand(
        @TargetAggregateIdentifier UUID registerId,
        String salt,
        Person person,
        List<Fee> fees,
        List<Tax> taxes,
        List<Tax> payedTaxes,
        List<QualificationsProof> qualificationsProofs,
        ConsentInfo consentInfo,
        UserDetails userDetails,
        Instant mockedTimestamp
) {

    public DigitizeRegularLicenseCommand {
        assert fees != null : "The list of fees should not be null";
        assert taxes != null : "The list of taxes should not be null";
        assert payedTaxes != null : "The list of payedTaxes should not be null";
        assert qualificationsProofs != null : "The list of qualificationsProofs should not be null";
    }
}
