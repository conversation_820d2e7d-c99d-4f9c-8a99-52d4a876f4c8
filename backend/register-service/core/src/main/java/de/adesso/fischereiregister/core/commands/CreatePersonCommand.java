package de.adesso.fischereiregister.core.commands;

import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import org.axonframework.modelling.command.TargetAggregateIdentifier;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public record CreatePersonCommand(
        @TargetAggregateIdentifier UUID registerId,
        Person person,
        List<Tax> taxes,
        List<Tax> payedTaxes,
        String salt,
        TaxConsentInfo consentInfo,
        UserDetails userDetails,
        Instant mockedTimestamp
) {

    public CreatePersonCommand {
        assert taxes != null : "The list of taxes should not be null";
        assert payedTaxes != null : "The list of payedTaxes should not be null";
    }
}
