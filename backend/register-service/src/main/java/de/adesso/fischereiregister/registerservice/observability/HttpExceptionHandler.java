package de.adesso.fischereiregister.registerservice.observability;

import de.adesso.fischereiregister.core.exceptions.ClientInputValidationException;
import de.adesso.fischereiregister.core.exceptions.CommandResultMismatchException;
import de.adesso.fischereiregister.core.exceptions.JurisdictionAlreadyAssignedException;
import de.adesso.fischereiregister.core.exceptions.JurisdictionMismatchException;
import de.adesso.fischereiregister.core.exceptions.LicenseApplicationNotRejectableException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotExtendableException;
import de.adesso.fischereiregister.core.exceptions.LicenseNotFoundException;
import de.adesso.fischereiregister.core.exceptions.OnlineServiceProcessingException;
import de.adesso.fischereiregister.core.exceptions.RestrictedDocumentDeliveryException;
import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.exceptions.TaxInWrongJurisdictionException;
import de.adesso.fischereiregister.registerservice.validation.ValidationResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.modelling.command.AggregateNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.format.DateTimeParseException;


@RestControllerAdvice
@Slf4j
public class HttpExceptionHandler {

    private final TraceIdHandler traceIdHandler;

    public HttpExceptionHandler(TraceIdHandler traceIdHandler) {
        this.traceIdHandler = traceIdHandler;
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(Exception exception) {
        log.error("Exception occurred: {}", exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(HttpMessageNotReadableException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(DateTimeParseException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(DateTimeParseException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(IllegalStateException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(ClientInputValidationException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public ResponseEntity<?> handleException(ClientInputValidationException exception) {
        log.error(exception.getMessage(), exception);
        return new ResponseEntity<>(ValidationResultMapper.INSTANCE.toRestResult(exception.getValidationResult()), HttpStatus.UNPROCESSABLE_ENTITY);
    }

    @ExceptionHandler(AggregateNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ExceptionResponse> handleException(AggregateNotFoundException exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.notFound().build();
    }

    @ExceptionHandler(LicenseNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ExceptionResponse> handleException(LicenseNotFoundException exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.notFound().build();
    }

    @ExceptionHandler(JurisdictionAlreadyAssignedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(JurisdictionAlreadyAssignedException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(TaxInWrongJurisdictionException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(TaxInWrongJurisdictionException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(RulesProcessingException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ExceptionResponse> handleException(RulesProcessingException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(JurisdictionMismatchException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseEntity<ExceptionResponse> handleException(JurisdictionMismatchException exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    @ExceptionHandler(CommandResultMismatchException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ExceptionResponse> handleException(CommandResultMismatchException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exceptionResponse);
    }

    @ExceptionHandler(LicenseNotExtendableException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ExceptionResponse> handleException(LicenseNotExtendableException exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.notFound().build();
    }

    @ExceptionHandler(OnlineServiceProcessingException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(OnlineServiceProcessingException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(LicenseApplicationNotRejectableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(LicenseApplicationNotRejectableException exception) {
        log.error("Unhandled exception occurred: {}", exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }

    @ExceptionHandler(RestrictedDocumentDeliveryException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ExceptionResponse> handleException(RestrictedDocumentDeliveryException exception) {
        log.error(exception.getMessage(), exception);
        final ExceptionResponse exceptionResponse = new ExceptionResponse(exception, traceIdHandler.getCorrelationId());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exceptionResponse);
    }
}