package de.adesso.fischereiregister.registerservice.import_testdata;

public class HeaderConstants {
    private HeaderConstants() {
        throw new UnsupportedOperationException("This static class should not be instantiated");
    }

    // Header constants
    public static final String LAST_NAME = "last_name";
    public static final String FIRST_NAME = "first_name";
    public static final String BIRTHNAME = "birthname";
    public static final String TITLE = "title";
    public static final String STREET = "street";
    public static final String STREET_NUMBER = "street_number";
    public static final String POST_CODE = "post_code";
    public static final String CITY = "city";
    public static final String FEDERAL_STATE = "federal_state";
    public static final String DATE_OF_BIRTH = "date_of_birth";
    public static final String BIRTHPLACE = "birthplace";
    public static final String NATIONALITY = "nationality";
    public static final String PRUEF_ID = "pruef_id";
    public static final String REGULAR_FL = "regular_fl";
    public static final String VACATION_FL = "vacation_fl";
    public static final String SPECIAL_FL = "special_fl";
    public static final String ISSUED_BY = "issued_by";
    public static final String FISHINGTAX_FROM = "fishingtax_from";
    public static final String FISHINGTAX_UNTIL = "fishingtax_until";
    public static final String FISHINGFEE_FROM = "fishingfee_from";
    public static final String FISHINGFEE_TO = "fishingfee_to";
    public static final String BAN_FROM = "ban_from";
    public static final String BAN_UNTIL = "ban_until";
    public static final String ENVIRONMENTS = "environments";
    public static final String QR_CODE = "qr_code";
    public static final String NFC_ADESSO_DP_NEIN = "nfc_adesso_dp_nein";
    public static final String KOMMENTAR = "kommentar";
    public static final String REGISTER_ID = "register_id";
    public static final String SALT = "salt";
    public static final String LICENSE_NUMBER = "license_number";
    public static final String TO_HASH = "to_hash";
    public static final String HASH = "hash";
    public static final String IDENTIFICATION_DOCUMENT_ID = "identification_document_id";
    public static final String QR_NFC = "qr_nfc";
    public static final String CERTIFICATE_ID = "certificate_id";
    public static final String EXAMINATION_PASSED_ON = "examination_passed_on";
    public static final String EXAMINATION_ISSUER = "examination_issuer";
    public static final String MOCKED_TIMESTAMP = "mocked_timestamp";

    // Array of all header constants
    protected static final String[] HEADERS;

    static {
        HEADERS = new String[]{
                LAST_NAME, FIRST_NAME, BIRTHNAME, TITLE, STREET, STREET_NUMBER, POST_CODE, CITY, FEDERAL_STATE,
                DATE_OF_BIRTH, BIRTHPLACE, NATIONALITY, PRUEF_ID,
                CERTIFICATE_ID, EXAMINATION_PASSED_ON, EXAMINATION_ISSUER,
                REGULAR_FL, VACATION_FL, SPECIAL_FL,
                ISSUED_BY, FISHINGFEE_FROM, FISHINGFEE_TO,
                FISHINGTAX_FROM, FISHINGTAX_UNTIL,
                BAN_FROM, BAN_UNTIL,
                ENVIRONMENTS, QR_CODE, NFC_ADESSO_DP_NEIN, KOMMENTAR, REGISTER_ID, SALT, LICENSE_NUMBER,
                TO_HASH, HASH, IDENTIFICATION_DOCUMENT_ID, QR_NFC, MOCKED_TIMESTAMP,
        };
    }
}
