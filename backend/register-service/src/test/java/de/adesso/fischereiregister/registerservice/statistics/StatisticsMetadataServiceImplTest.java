package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.fees_statistics.services.FeesStatisticsViewService;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StatisticsMetadataServiceImplTest {

    @Mock
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Mock
    private TaxesStatisticsViewService taxesStatisticsViewService;

    @Mock
    private LicensesStatisticsViewService licensesStatisticsViewService;

    @Mock
    private FeesStatisticsViewService feesStatisticsViewService;

    @Mock
    private InspectorProtocolService inspectorProtocolService;

    @Mock
    private BanViewService banViewService;

    private StatisticsMetadataServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new StatisticsMetadataServiceImpl(
                certificationsStatisticsViewService,
                taxesStatisticsViewService,
                licensesStatisticsViewService,
                feesStatisticsViewService,
                inspectorProtocolService,
                banViewService
        );
    }

    @Test
    @DisplayName("Should get available certification issuers without filters")
    void shouldGetAvailableCertificationIssuersWithoutFilters() {
        // given
        List<String> expectedIssuers = List.of("Office1", "Office2");
        when(certificationsStatisticsViewService.getAvailableIssuers()).thenReturn(expectedIssuers);

        // when
        List<String> result = service.getAvailableCertificationIssuers();

        // then
        assertThat(result).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuers();
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get available certification issuers filtered by years")
    void shouldGetAvailableCertificationIssuersFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<String> expectedIssuers = List.of("Office1", "Office2");
        when(certificationsStatisticsViewService.getAvailableIssuersByYears(years)).thenReturn(expectedIssuers);

        // when
        List<String> result = service.getAvailableCertificationIssuersByYears(years);

        // then
        assertThat(result).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByYears(years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get available certification issuers filtered by federal state")
    void shouldGetAvailableCertificationIssuersFilteredByFederalState() {
        // given
        String federalState = "SH";
        List<String> expectedIssuers = List.of("Office1", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuersByFederalState(federalState)).thenReturn(expectedIssuers);

        // when
        List<String> result = service.getAvailableCertificationIssuersByFederalState(federalState);

        // then
        assertThat(result).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByFederalState(federalState);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should get available certification issuers filtered by years and federal state")
    void shouldGetAvailableCertificationIssuersFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        String federalState = "SH";
        List<String> expectedIssuers = List.of("Office1", "Office3");
        when(certificationsStatisticsViewService.getAvailableIssuersByYearsAndFederalState(years, federalState)).thenReturn(expectedIssuers);

        // when
        List<String> result = service.getAvailableCertificationIssuersByYearsAndFederalState(years, federalState);

        // then
        assertThat(result).isEqualTo(expectedIssuers);
        verify(certificationsStatisticsViewService).getAvailableIssuersByYearsAndFederalState(years, federalState);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("Should aggregate offices from taxes and licenses statistics without filters")
    void shouldAggregateOfficesWithoutFilters() {
        // given
        List<String> taxOffices = List.of("TaxOffice1", "TaxOffice2", "SharedOffice");
        List<String> licenseOffices = List.of("LicenseOffice1", "SharedOffice", "LicenseOffice2");
        
        when(taxesStatisticsViewService.getAvailableOffices()).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOffices()).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOffices();

        // then
        assertThat(result).containsExactly("LicenseOffice1", "LicenseOffice2", "SharedOffice", "TaxOffice1", "TaxOffice2");
        verify(taxesStatisticsViewService).getAvailableOffices();
        verify(licensesStatisticsViewService).getAvailableOffices();
        verifyNoMoreInteractions(taxesStatisticsViewService, licensesStatisticsViewService);
    }

    @Test
    @DisplayName("Should aggregate offices from taxes and licenses statistics filtered by years")
    void shouldAggregateOfficesFilteredByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<String> taxOffices = List.of("TaxOffice1", "SharedOffice");
        List<String> licenseOffices = List.of("LicenseOffice1", "SharedOffice");
        
        when(taxesStatisticsViewService.getAvailableOfficesByYears(years)).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOfficesByYears(years)).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOfficesByYears(years);

        // then
        assertThat(result).containsExactly("LicenseOffice1", "SharedOffice", "TaxOffice1");
        verify(taxesStatisticsViewService).getAvailableOfficesByYears(years);
        verify(licensesStatisticsViewService).getAvailableOfficesByYears(years);
        verifyNoMoreInteractions(taxesStatisticsViewService, licensesStatisticsViewService);
    }

    @Test
    @DisplayName("Should aggregate offices from taxes and licenses statistics filtered by federal state")
    void shouldAggregateOfficesFilteredByFederalState() {
        // given
        String federalState = "SH";
        List<String> taxOffices = List.of("TaxOffice1", "SharedOffice");
        List<String> licenseOffices = List.of("LicenseOffice1", "SharedOffice");
        
        when(taxesStatisticsViewService.getAvailableOfficesByFederalState(federalState)).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOfficesByFederalState(federalState)).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOfficesByFederalState(federalState);

        // then
        assertThat(result).containsExactly("LicenseOffice1", "SharedOffice", "TaxOffice1");
        verify(taxesStatisticsViewService).getAvailableOfficesByFederalState(federalState);
        verify(licensesStatisticsViewService).getAvailableOfficesByFederalState(federalState);
        verifyNoMoreInteractions(taxesStatisticsViewService, licensesStatisticsViewService);
    }

    @Test
    @DisplayName("Should aggregate offices from taxes and licenses statistics filtered by years and federal state")
    void shouldAggregateOfficesFilteredByYearsAndFederalState() {
        // given
        List<Integer> years = List.of(2023, 2024);
        String federalState = "SH";
        List<String> taxOffices = List.of("TaxOffice1", "SharedOffice");
        List<String> licenseOffices = List.of("LicenseOffice1", "SharedOffice");
        
        when(taxesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(years, federalState)).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOfficesByYearsAndFederalState(years, federalState)).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOfficesByYearsAndFederalState(years, federalState);

        // then
        assertThat(result).containsExactly("LicenseOffice1", "SharedOffice", "TaxOffice1");
        verify(taxesStatisticsViewService).getAvailableOfficesByYearsAndFederalState(years, federalState);
        verify(licensesStatisticsViewService).getAvailableOfficesByYearsAndFederalState(years, federalState);
        verifyNoMoreInteractions(taxesStatisticsViewService, licensesStatisticsViewService);
    }

    @Test
    @DisplayName("Should handle empty lists from one service")
    void shouldHandleEmptyListsFromOneService() {
        // given
        List<String> taxOffices = List.of("TaxOffice1", "TaxOffice2");
        List<String> licenseOffices = List.of(); // Empty list
        
        when(taxesStatisticsViewService.getAvailableOffices()).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOffices()).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOffices();

        // then
        assertThat(result).containsExactly("TaxOffice1", "TaxOffice2");
        verify(taxesStatisticsViewService).getAvailableOffices();
        verify(licensesStatisticsViewService).getAvailableOffices();
    }

    @Test
    @DisplayName("Should handle empty lists from both services")
    void shouldHandleEmptyListsFromBothServices() {
        // given
        List<String> taxOffices = List.of();
        List<String> licenseOffices = List.of();
        
        when(taxesStatisticsViewService.getAvailableOffices()).thenReturn(taxOffices);
        when(licensesStatisticsViewService.getAvailableOffices()).thenReturn(licenseOffices);

        // when
        List<String> result = service.getAvailableOffices();

        // then
        assertThat(result).isEmpty();
        verify(taxesStatisticsViewService).getAvailableOffices();
        verify(licensesStatisticsViewService).getAvailableOffices();
    }

    @Test
    @DisplayName("Should aggregate years from all statistics sources")
    void shouldAggregateYearsFromAllSources() {
        // given
        List<Integer> certificationYears = List.of(2024, 2023, 2022);
        List<Integer> taxYears = List.of(2024, 2023, 2021);
        List<Integer> licenseYears = List.of(2024, 2020);
        List<Integer> banYears = List.of(2023, 2022);
        List<Integer> feeYears = List.of(2024, 2021, 2019);
        List<Integer> inspectionYears = List.of(2024, 2023, 2018);

        when(certificationsStatisticsViewService.getAvailableYears()).thenReturn(certificationYears);
        when(taxesStatisticsViewService.getAvailableYears()).thenReturn(taxYears);
        when(licensesStatisticsViewService.getAvailableYears()).thenReturn(licenseYears);
        when(banViewService.getAvailableYears()).thenReturn(banYears);
        when(feesStatisticsViewService.getAvailableYears()).thenReturn(feeYears);
        when(inspectorProtocolService.getAvailableYears()).thenReturn(inspectionYears);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertThat(result).containsExactly(2024, 2023, 2022, 2021, 2020, 2019, 2018); // Descending order, no duplicates
        verify(certificationsStatisticsViewService).getAvailableYears();
        verify(taxesStatisticsViewService).getAvailableYears();
        verify(licensesStatisticsViewService).getAvailableYears();
        verify(banViewService).getAvailableYears();
        verify(feesStatisticsViewService).getAvailableYears();
        verify(inspectorProtocolService).getAvailableYears();
        verifyNoMoreInteractions(certificationsStatisticsViewService, taxesStatisticsViewService,
                licensesStatisticsViewService, banViewService, feesStatisticsViewService, inspectorProtocolService);
    }

    @Test
    @DisplayName("Should handle empty lists from some statistics sources")
    void shouldHandleEmptyListsFromSomeSources() {
        // given
        List<Integer> certificationYears = List.of(2024, 2023);
        List<Integer> taxYears = List.of(); // Empty
        List<Integer> licenseYears = List.of(2022);
        List<Integer> banYears = List.of(); // Empty
        List<Integer> feeYears = List.of(2024, 2021);
        List<Integer> inspectionYears = List.of(); // Empty

        when(certificationsStatisticsViewService.getAvailableYears()).thenReturn(certificationYears);
        when(taxesStatisticsViewService.getAvailableYears()).thenReturn(taxYears);
        when(licensesStatisticsViewService.getAvailableYears()).thenReturn(licenseYears);
        when(banViewService.getAvailableYears()).thenReturn(banYears);
        when(feesStatisticsViewService.getAvailableYears()).thenReturn(feeYears);
        when(inspectorProtocolService.getAvailableYears()).thenReturn(inspectionYears);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertThat(result).containsExactly(2024, 2023, 2022, 2021); // Descending order, no duplicates
        verify(certificationsStatisticsViewService).getAvailableYears();
        verify(taxesStatisticsViewService).getAvailableYears();
        verify(licensesStatisticsViewService).getAvailableYears();
        verify(banViewService).getAvailableYears();
        verify(feesStatisticsViewService).getAvailableYears();
        verify(inspectorProtocolService).getAvailableYears();
    }
}
